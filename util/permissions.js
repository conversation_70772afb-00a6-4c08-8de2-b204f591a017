/**
 * Permission utility functions for checking user access rights
 * Based on $auth.user.permissions array structure
 */

/**
 * Check if user has a specific permission by name
 * @param {Object} user - The authenticated user object
 * @param {string} permissionName - Permission name (e.g., "claims.create")
 * @returns {boolean}
 */
export function hasPermission(user, permissionName) {
    if (!user || !user.permissions || !Array.isArray(user.permissions)) return false
    return user.permissions.some(permission => permission.name === permissionName)
}

/**
 * Check if user has permission by resource and action
 * @param {Object} user - The authenticated user object
 * @param {string} resource - Resource name (e.g., "claims")
 * @param {string} action - Action name (e.g., "create")
 * @returns {boolean}
 */
export function hasPermissionByResourceAction(user, resource, action) {
    if (!user || !user.permissions || !Array.isArray(user.permissions)) return false
    return user.permissions.some(permission =>
        permission.resource === resource && permission.action === action
    )
}

/**
 * Check if user has any of the specified permissions
 * @param {Object} user - The authenticated user object
 * @param {Array<string>} permissionNames - Array of permission names to check
 * @returns {boolean}
 */
export function hasAnyPermission(user, permissionNames) {
    if (!user || !user.permissions || !Array.isArray(user.permissions) || !Array.isArray(permissionNames)) return false
    return permissionNames.some(permissionName => hasPermission(user, permissionName))
}

/**
 * Check if user has all of the specified permissions
 * @param {Object} user - The authenticated user object
 * @param {Array<string>} permissionNames - Array of permission names to check
 * @returns {boolean}
 */
export function hasAllPermissions(user, permissionNames) {
    if (!user || !user.permissions || !Array.isArray(user.permissions) || !Array.isArray(permissionNames)) return false
    return permissionNames.every(permissionName => hasPermission(user, permissionName))
}

/**
 * Get all permission names for a user
 * @param {Object} user - The authenticated user object
 * @returns {Array<string>} Array of permission names
 */
export function getUserPermissionNames(user) {
    if (!user || !user.permissions || !Array.isArray(user.permissions)) return []
    return user.permissions.map(permission => permission.name)
}

/**
 * Get all permissions for a user
 * @param {Object} user - The authenticated user object
 * @returns {Array<Object>} Array of permission objects
 */
export function getUserPermissions(user) {
    if (!user || !user.permissions || !Array.isArray(user.permissions)) return []
    return user.permissions
}

/**
 * Get permissions by resource
 * @param {Object} user - The authenticated user object
 * @param {string} resource - Resource name
 * @returns {Array<Object>} Array of permission objects for the resource
 */
export function getPermissionsByResource(user, resource) {
    if (!user || !user.permissions || !Array.isArray(user.permissions)) return []
    return user.permissions.filter(permission => permission.resource === resource)
}

/**
 * Check if user can perform action on resource
 * @param {Object} user - The authenticated user object
 * @param {string} resource - Resource name
 * @param {string} action - Action name
 * @returns {boolean}
 */
export function canPerformAction(user, resource, action) {
    return hasPermissionByResourceAction(user, resource, action)
}

// Specific permission checks based on your system
/**
 * Check if user has permission to create claims
 * @param {Object} user - The authenticated user object
 * @returns {boolean}
 */
export function canCreateClaim(user) {
    return hasPermission(user, 'claims.create')
}

/**
 * Check if user has permission to view claims
 * @param {Object} user - The authenticated user object
 * @returns {boolean}
 */
export function canViewClaim(user) {
    return hasPermission(user, 'claims.view') || hasPermission(user, 'claims.index')
}

/**
 * Check if user has permission to edit claims
 * @param {Object} user - The authenticated user object
 * @returns {boolean}
 */
export function canEditClaim(user) {
    return hasPermission(user, 'claims.edit') || hasPermission(user, 'claims.update')
}

/**
 * Check if user has permission to delete claims
 * @param {Object} user - The authenticated user object
 * @returns {boolean}
 */
export function canDeleteClaim(user) {
    return hasPermission(user, 'claims.delete')
}

/**
 * Check if user has permission to manage gifts
 * @param {Object} user - The authenticated user object
 * @returns {boolean}
 */
export function canManageGifts(user) {
    return hasAnyPermission(user, ['gifts.create', 'gifts.edit', 'gifts.delete', 'gifts.manage'])
}

/**
 * Check if user has permission to manage campaigns
 * @param {Object} user - The authenticated user object
 * @returns {boolean}
 */
export function canManageCampaigns(user) {
    return hasAnyPermission(user, ['campaigns.create', 'campaigns.edit', 'campaigns.delete', 'campaigns.manage'])
}

/**
 * Check if user has permission to manage whitelist
 * @param {Object} user - The authenticated user object
 * @returns {boolean}
 */
export function canManageWhitelist(user) {
    return hasAnyPermission(user, ['whitelist.create', 'whitelist.edit', 'whitelist.delete', 'whitelist.manage'])
}

/**
 * Check if user has permission to upload images
 * @param {Object} user - The authenticated user object
 * @returns {boolean}
 */
export function canUploadImages(user) {
    return hasPermission(user, 'images.upload') || hasPermission(user, 'images.create')
}

/**
 * Check if user has admin permissions
 * @param {Object} user - The authenticated user object
 * @returns {boolean}
 */
export function isAdmin(user) {
    return hasAnyPermission(user, ['admin.*', 'admin.access', 'super.admin'])
}

/**
 * Check if user has any management permissions
 * @param {Object} user - The authenticated user object
 * @returns {boolean}
 */
export function hasManagementAccess(user) {
    const managementPermissions = getUserPermissions(user).filter(permission =>
        permission.action === 'create' ||
        permission.action === 'edit' ||
        permission.action === 'delete' ||
        permission.action === 'manage'
    )
    return managementPermissions.length > 0
}

/**
 * Check if user has permission to change OCR data
 * @param {Object} user - The authenticated user object
 * @returns {boolean}
 */
export function canChangeOcr(user) {
    return hasAnyPermission(user, ['claims.edit', 'claims.update', 'ocr.edit', 'ocr.change']) || canEditClaim(user)
}

/**
 * Check if user has permission to create users
 * @param {Object} user - The authenticated user object
 * @returns {boolean}
 */
export function canCreateUser(user) {
    return hasPermissionByResourceAction(user, 'users', 'create') || hasPermission(user, 'users.create')
}

/**
 * Check if user has permission to update users
 * @param {Object} user - The authenticated user object
 * @returns {boolean}
 */
export function canUpdateUser(user) {
    return hasPermissionByResourceAction(user, 'users', 'update') || hasPermission(user, 'users.update')
}

/**
 * Check if user has permission to read/view users
 * @param {Object} user - The authenticated user object
 * @returns {boolean}
 */
export function canViewUsers(user) {
    return hasPermissionByResourceAction(user, 'users', 'read') || hasPermission(user, 'users.read')
}

/**
 * Check if user has permission to delete users
 * @param {Object} user - The authenticated user object
 * @returns {boolean}
 */
export function canDeleteUser(user) {
    return hasPermissionByResourceAction(user, 'users', 'delete') || hasPermission(user, 'users.delete')
}

/**
 * Check if user has permission to manage users (any user management action)
 * @param {Object} user - The authenticated user object
 * @returns {boolean}
 */
export function canManageUsers(user) {
    return hasAnyPermission(user, ['users.create', 'users.update', 'users.delete', 'users.manage']) ||
        canCreateUser(user) || canUpdateUser(user) || canDeleteUser(user)
}

/**
 * Check if user has permission to update orders
 * @param {Object} user - The authenticated user object
 * @returns {boolean}
 */
export function canUpdateOrder(user) {
    return hasAnyPermission(user, ['orders.edit', 'orders.update', 'claims.edit', 'claims.update']) || canEditClaim(user)
}

/**
 * Check if user CANNOT update orders
 * @param {Object} user - The authenticated user object
 * @returns {boolean}
 */
export function cantUpdateOrder(user) {
    return !canUpdateOrder(user)
}

/**
 * Check if user has admin or super admin permissions
 * @param {Object} user - The authenticated user object
 * @returns {boolean}
 */
export function isSuperAdmin(user) {
    return hasAnyPermission(user, ['super.admin', 'admin.*', 'root.access'])
}

/**
 * Check if user has permission to read claims_temp
 * @param {Object} user - The authenticated user object
 * @returns {boolean}
 */
export function canReadClaimsTemp(user) {
    return hasPermission(user, 'claims_temp.read');
}

/**
 * Check if user has permission to update claims_temp
 * @param {Object} user - The authenticated user object
 * @returns {boolean}
 */
export function canUpdateClaimsTemp(user) {
    return hasPermission(user, 'claims_temp.update');
}

/**
 * Check if user has permission to delete claims_temp
 * @param {Object} user - The authenticated user object
 * @returns {boolean}
 */
export function canDeleteClaimsTemp(user) {
    return hasPermission(user, 'claims_temp.delete');
}

/**
 * Check if user has permission to delete serial claims_temp
 * @param {Object} user - The authenticated user object
 * @returns {boolean}
 */
export function canDeleteSerialClaimsTemp(user) {
    return hasPermission(user, 'claims_temp.delete_serial');
}


/**
 * Check if user has permission to approve claims_temp
 * @param {Object} user - The authenticated user object
 * @returns {boolean}
 */
export function canApproveClaimsTemp(user) {
    return hasPermission(user, 'claims_temp.approve');
}

/**
 * Check if user has permission to export claims_temp
 * @param {Object} user - The authenticated user object
 * @returns {boolean}
 */
export function canExportClaimsTemp(user) {
    return hasPermission(user, 'claims_temp.export');
} 