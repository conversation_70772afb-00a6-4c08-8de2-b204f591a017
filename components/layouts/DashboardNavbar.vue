<template>
  <base-nav container-classes="container-fluid" class="navbar-top border-bottom navbar-expand"
    :class="{ 'bg-success navbar-dark': type === 'default' }">
    <!-- Navbar links -->
    <ul class="navbar-nav align-items-center ml-md-auto">
      <li class="nav-item d-xl-none">
        <!-- Sidenav toggler -->
        <div class="pr-3 sidenav-toggler" :class="{
          active: $sidebar.showSidebar,
          'sidenav-toggler-dark': type === 'default',
          'sidenav-toggler-light': type === 'light',
        }" @click="toggleSidebar">
          <div class="sidenav-toggler-inner">
            <i class="sidenav-toggler-line"></i>
            <i class="sidenav-toggler-line"></i>
            <i class="sidenav-toggler-line"></i>
          </div>
        </div>
      </li>
    </ul>
    <ul class="navbar-nav align-items-center ml-auto ml-md-0">
      <li>
        <el-menu :default-active="activeIndex" class="phase-list" mode="horizontal" @select="handleSelect">
          <el-submenu index="0">
            <template slot="title">{{ currentCampaign.title }}</template>
            <el-menu-item @click="handleChangeCampaign(campaign)" v-for="(campaign, index) in campaigns"
              v-if="campaign.end_time_for_admin === null || campaign.end_time_for_admin > (new Date().getTime() / 1000 - (86400 * 15))"
              :key="index" :index="`${index} - ${index + 1}`">
              {{ campaign.title }}
            </el-menu-item>
          </el-submenu>
        </el-menu>
      </li>
      <base-dropdown menu-on-right class="nav-item" tag="li" title-tag="a" title-classes="nav-link pr-0">
        <a href="#" class="nav-link pr-0" @click.prevent slot="title-container">
          <div class="media align-items-center">
            <span class="avatar avatar-sm rounded-circle avatar-image" :style="{
              'background-image': `url('${profileImage}')`,
            }"></span>
            <div class="media-body ml-2 d-none d-lg-block">
              <span class="mb-0 text-sm font-weight-bold">{{ user.name }}</span>
            </div>
          </div>
        </a>

        <template>
          <div class="dropdown-header noti-title">
            <h6 class="text-overflow m-0">Welcome!</h6>
          </div>

          <div to="" class="dropdown-item">
            <i class="el-icon-user-solid"></i>
            <span>{{ user?.role?.display_name }}</span>
          </div>

          <a @click.prevent="logout()" to="" class="dropdown-item">
            <i class="ni ni-user-run"></i>
            <span>Logout</span>
          </a>
          <a @click.prevent="changePassword()" to="" class="dropdown-item">
            <i class="el-icon-lock"></i>
            <span>Đổi mật khẩu</span>
          </a>
        </template>
      </base-dropdown>
    </ul>
  </base-nav>
</template>
<script>
import { CollapseTransition } from 'vue2-transitions';
import BaseNav from '@/components/argon-core/Navbar/BaseNav.vue';
import Modal from '@/components/argon-core/Modal.vue';
import { CAMPAIGN_LIST } from '~/util/constant';

export default {
  components: {
    CollapseTransition,
    BaseNav,
    Modal,
  },
  props: {
    type: {
      type: String,
      default: 'default', // default|light
      description: 'Look of the dashboard navbar. Default (Green) or light (gray)',
    },
  },
  computed: {
    currentCampaign() {
      return this.$store.getters['campaignStore/currentCampaign'];
    },
    campaigns() {
      if (this.user?.id && this.user?.id === 4)
        return this.$store.getters['campaignStore/campaigns'];
      const campaignList = [];
      this.$store.getters['campaignStore/campaigns']?.forEach(campaign => {
        if (campaign.status === 2) {
          campaignList.push(campaign)
        }
      })
      return campaignList;


    },
    routeName() {
      const { name } = this.$route;
      return this.capitalizeFirstLetter(name);
    },
    user() {
      return this.$auth.user;
    },
    profileImage() {
      return this.$auth && this.$auth.user && this.$auth.user.profile_image
        ? this.$auth.user.profile_image
        : '/img/placeholder.jpg';
    },
  },
  data() {
    return {
      activeNotifications: false,
      showMenu: false,
      searchModalVisible: false,
      searchQuery: '',
      activeIndex: '1',
      campaignList: CAMPAIGN_LIST,
    };
  },
  methods: {
    handleSelect(key, keyPath) {
      // console.log(key, keyPath);
    },
    async handleChangeCampaign(campaign) {
      await this.$store.dispatch('campaignStore/setCurrentCampaign', campaign);
      location.reload();
    },
    capitalizeFirstLetter(string) {
      return string.charAt(0).toUpperCase() + string.slice(1);
    },
    toggleNotificationDropDown() {
      this.activeNotifications = !this.activeNotifications;
    },
    closeDropDown() {
      this.activeNotifications = false;
    },
    toggleSidebar() {
      this.$sidebar.displaySidebar(!this.$sidebar.showSidebar);
    },
    hideSidebar() {
      this.$sidebar.displaySidebar(false);
    },
    async logout() {
      try {
        await this.$auth.logout();
        await this.$router.push('/login');
      } catch (error) {
        this.$toast.error(error.response.message);
      }
    },
    changePassword() {
      this.$router.push('/change-password');
    },
  },
};
</script>
<style lang="scss">
.phase-list .el-submenu__title {
  height: 40px !important;
  line-height: 40px !important;
  background: transparent;
  color: white !important;

  &:hover {
    background: transparent;
  }

  .el-submenu__icon-arrow {
    color: white !important;
  }
}

.phase-list.el-menu--horizontal>.el-submenu .el-submenu__title {
  border: none !important;

  &:hover {
    background: transparent;
  }
}

.phase-list.el-menu.el-menu--horizontal {
  border: none !important;
}

.phase-list.el-menu {
  color: white !important;
  background: transparent !important;

  &:hover {
    background: transparent;
  }
}
</style>
