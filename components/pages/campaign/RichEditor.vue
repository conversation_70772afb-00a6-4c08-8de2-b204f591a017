<script>
import Editor from '@tinymce/tinymce-vue'
export default {
    components: {
        Editor
    },
    model: {
        prop: 'tnc',
        event: 'update:tnc'
    },
    props: {
        tnc: {
            type: String,
            required: true
        }
    },
    data() {
        return {
            content: this.tnc
        }
    },
    watch: {
        content(newVal) {
            this.$emit('update:tnc', newVal);
        },
        tnc(newVal) {
            if (newVal !== this.content) {
                this.content = newVal;
            }
        }
    }
}
</script>

<template>
    <main id="sample">
        <editor id="uuid" apiKey="xdlp9h5s91vu5lx70vb1cxuzhl4o9op44bx89toreudekr3s" :init="{
            plugins: 'advlist anchor autolink charmap code fullscreen help image insertdatetime link lists media preview searchreplace table visualblocks wordcount',
            toolbar: 'undo redo | styles | bold italic underline strikethrough | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | link image',
            height: 500,
        }" v-model="content">
        </editor>
    </main>
</template>