<script>
import Editor from '@tinymce/tinymce-vue'
export default {
    components: {
        Editor
    },
    model: {
        prop: 'tnc',
        event: 'update:tnc'
    },
    props: {
        tnc: {
            type: String,
            required: true
        }
    },
    data() {
        return {
            content: this.tnc
        }
    },
    watch: {
        content(newVal) {
            this.$emit('update:tnc', newVal);
        },
        tnc(newVal) {
            if (newVal !== this.content) {
                this.content = newVal;
            }
        }
    }
}
</script>

<template>
    <main id="sample">
        <editor id="uuid" apiKey="xdlp9h5s91vu5lx70vb1cxuzhl4o9op44bx89toreudekr3s" :init="{
            plugins: 'advlist anchor autolink charmap code fullscreen help image insertdatetime link lists media preview searchreplace table visualblocks wordcount',
            toolbar: 'undo redo | styles | fontselect | bold italic underline strikethrough | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | link image',
            height: 500,
            font_formats: 'Roboto=Roboto,sans-serif; Open Sans=Open Sans,sans-serif; Arial=arial,helvetica,sans-serif; Arial Black=arial black,avant garde; Book Antiqua=book antiqua,palatino; Comic Sans MS=comic sans ms,sans-serif; Courier New=courier new,courier; Georgia=georgia,palatino; Helvetica=helvetica; Impact=impact,chicago; Symbol=symbol; Tahoma=tahoma,arial,helvetica,sans-serif; Terminal=terminal,monaco; Times New Roman=times new roman,times; Trebuchet MS=trebuchet ms,geneva; Verdana=verdana,geneva; Webdings=webdings; Wingdings=wingdings,zapf dingbats',
            content_style: 'body { font-family: Roboto,sans-serif; font-size: 14px; }'
        }" v-model="content">
        </editor>
    </main>
</template>