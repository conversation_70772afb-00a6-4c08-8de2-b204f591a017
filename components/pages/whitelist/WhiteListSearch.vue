<template>
  <div class="row pt-4">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title mb-0">{{ currentCampaign.title }}</h3>
        </div>

        <div class="card-body">
          <form>
            <div class="row">
              <div class="col-lg-3 col-md-6 mb-4">
                <el-input @change="handleCodecheckChange" v-model="params.code_check" placeholder="Code check" ></el-input>
              </div>
              <div class="col-lg-3 col-md-6 mb-4">
                <el-select
                  class="w-100"
                  @change="handleProcessChange"
                  clearable
                  v-model="params.status"
                  placeholder="Trạng thái"
                >
                  <el-option
                    v-for="option in claimProcessType"
                    :key="option.VALUE"
                    :label="option.TEXT"
                    :value="option.VALUE"
                  ></el-option>
                </el-select>
                
              </div>
              <div class="col-lg-3 col-md-6 mb-4" ><el-button class="col-sm-6" type="primary" icon="el-icon-search" @click="onSearch()">
                  Tìm kiếm
                </el-button>
              </div>
          
              <div class="col-lg-12 d-flex justify-content-end">
             
                <el-button v-if="$permissions.hasPermission('whitelist.import')" class="col-lg-2 col-md-2 col-sm-12" type="warning" icon="el-icon-upload" @click="importDialogVisible = true">
                  Import whitelist(OPS)
                </el-button>
                <el-button v-if="$permissions.hasPermission('whitelist.create')" @click="dialogVisible = !dialogVisible" type="primary" icon="el-icon-upload">
                  Import white-list (KAM)
                </el-button>
                <el-button v-if="$permissions.hasPermission('whitelist.create')" class="col-lg-2 col-md-2 col-sm-12" type="success" icon="el-icon-circle-plus" @click="onCreate()">
                  Thêm mới
                </el-button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
    <div v-if="$permissions.hasPermission('whitelist.create')">
            
            <ValidationObserver v-slot="{ invalid }">
              <el-dialog
                class="dialog-export"
                title="Import danh sách white-list"
                :visible.sync="dialogVisible"
                close-on-click-modal
              >
                <label for="fname">Thêm danh sách white-list bằng file excel (tối đa 1000 dòng)</label>
                <input
                    name="fname"
                    id="fname"
                    type="file"
                    accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                    @change="uploadFile"
                    placeholder="File excel serial"
                ><br><br>
                <a href="https://file-cdn.urbox.dev/whitelist1678196880328163378.xlsx" download>Tải file mẫu!</a><br><br>
                <el-button type="success" icon="el-icon-circle-plus" @click="submitFile">
                    Upload!
                </el-button>
              </el-dialog>
            </ValidationObserver>
            
          </div>
    <el-dialog
    title="Import whitelist"
    :visible.sync="importDialogVisible"
    close-on-click-modal
    size="small"
    width="500px"
  >
    <label for="import-fname">Chọn file Excel whitelist</label>
    <input
      name="import-fname"
      id="import-fname"
      type="file"
      accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
      @change="onImportFileChange"
      placeholder="File excel whitelist"
    ><br><br>
    <a href="/assets/whitelist-for-ops.xlsx" download>Tải file mẫu!</a><br><br>
    <el-button type="success" icon="el-icon-circle-plus" @click="onImportSubmit">
      Upload
    </el-button>
  </el-dialog>
  </div>
 
</template>

<script>
import {computed, defineComponent, ref, useAsync, useContext, useStore} from '@nuxtjs/composition-api';
  import { URBOX_STATUS } from '@/util/constant.js';
  import { Select, Option, DatePicker, Button } from 'element-ui';
  import useWhiteList from '@/composition/useWhiteList.js';
  import useStores from '@/composition/useStores.js';
  import { Message } from 'element-ui';

  export default defineComponent({
    name: 'WhiteListSearch',
    props: {
      params: Object,
    },
    components: {
      [Select.name]: Select,
      [Option.name]: Option,
      [DatePicker.name]: DatePicker,
      [Button.name]: Button,
    },
    setup(props) {
      // const store = useStore();
      const { store } = useContext();
      const isLoading = ref(false);
      const dialogVisible = ref(false);
      const importDialogVisible = ref(false);
      const { createWhiteList, importExcelSerial, fetchWhiteList, importExcelOps} = useWhiteList();
      const { fetchStores } = useStores();
      const campaign = store.getters['campaignStore/campaigns'];
      const dateRange = ref(null);
      const currentCampaign = store.getters['campaignStore/currentCampaign'];
      let serialFile = null;
      const importFile = ref(null);

      useAsync(async () => {
        await fetchStores({});
      });

      const stores = computed(() => {
        return store.getters['store/items'];
      });

      const onSearch = async () => {
        await store.dispatch('setLoading', true);
          props.params.page = 1;
          props.params.code_check = props.params.code_check == null ? null: props.params.code_check.trim();
          await fetchWhiteList(props.params);
          await store.dispatch('setLoading', false);
      };

      const onCreate = async () => {
        if(props.params.code_check == null){
          Message({
              message: 'Vui lòng nhập code check',
              type: 'danger',
              duration: 5000,
            });
        }
        if(props.params.code_check !== null  && confirm('Bạn muốn thêm mới code check '+props.params.code_check.trim()+' ?')){
          props.params.page = 1;
          props.params.code_check = props.params.code_check.trim();
          const response = await createWhiteList(props.params);
          if(response.code && response.code === 200){
            Message({
              message: 'Thêm mới code check thành cônng',
              type: 'success',
              duration: 5000,
            });
            await fetchWhiteList(props.params);
          }
        }
      };

      const uploadFile = async (event) => {
        console.log(currentCampaign)
        console.log(currentCampaign.id)
        console.log(event.target.files)
        serialFile = event.target.files[0];
        console.log(serialFile)
      };
      const submitFile = async (e) => {
        e.preventDefault()
        await store.dispatch('setLoading', true);
        console.log(currentCampaign)
        console.log(currentCampaign.id)
        console.log(serialFile)
        if (serialFile != null) {
          console.log("Serial file not null")
          let data = await importExcelSerial(serialFile, currentCampaign.id);
          console.log(data)
          if (data?.code === 200) {
            Message({
                message: data.msg,
                type: 'success',
                duration: 5000,
              });
            }
        } else {
          Message({
            message: "File không được để trống",
            type: 'error',
            duration: 5000,
          });
        }
        await store.dispatch('setLoading', false);
      };

      const handleStoreAddressChange = async (search) => {
        isLoading.value = true;
        await fetchStores({ search });
        isLoading.value = false;
      };

      const onOrderStoreChange = (data) => {
        if (!data) {
          props.params.orderStoreId = null;
          handleStoreAddressChange('');
        }
      };

      const handleProcessChange = (input) => {
        if (!input) {
          props.params.status = null;
        }
      };

       const handleCodecheckChange = (input) => {
        if (!input) {
          props.params.code_check = null;
        }
      };

      const claimProcessType = URBOX_STATUS;

      const onImportFileChange = (event) => {
        importFile.value = event.target.files[0];
      };
      const onImportSubmit = async () => {
        if (!importFile.value) {
          Message({
            message: 'Vui lòng chọn file Excel',
            type: 'error',
            duration: 5000,
          });
          return;
        }
        await store.dispatch('setLoading', true);
        const data = await importExcelOps(importFile.value, currentCampaign.id);
        if (data?.code === 200) {
          Message({
            message: data.msg,
            type: 'success',
            duration: 5000,
          });
          importDialogVisible.value = false;
          importFile.value = null;
          await fetchWhiteList(props.params);
        } else {
          Message({
            message: data?.msg || 'Lỗi import whitelist',
            type: 'error',
            duration: 5000,
          });
        }
        await store.dispatch('setLoading', false);
      };

      return {
        claimProcessType,
        currentCampaign,
        campaign,
        onSearch,
        onCreate,
        dateRange,
        stores,
        handleStoreAddressChange,
        isLoading,
        onOrderStoreChange,
        handleProcessChange,
        handleCodecheckChange,
        uploadFile,
        submitFile,
        dialogVisible,
        importDialogVisible,
        importFile,
        onImportFileChange,
        onImportSubmit,
      };
    },
  });
</script>

<style lang="scss">
  .el-select .el-input .el-input__inner {
    height: 40px;
  }

  .el-date-editor {
    .el-range-separator {
      width: 10% !important;
    }
    .el-icon-date {
      margin-left: 0;
    }
    i {
      width: 8% !important;
    }
  }
</style>
