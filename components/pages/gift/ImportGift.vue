<template>
  <div>
    <el-button @click="dialogVisible = !dialogVisible" v-if="$permissions.hasPermission('gifts.create')" type="primary"
      icon="el-icon-circle-plus" size="mini">
      Import danh sách quà
    </el-button>
    <ValidationObserver v-slot="{ invalid }">
      <el-dialog class="dialog-export" title="Import danh sách quà" :visible.sync="dialogVisible"
        :before-close="handleBeforeCloseDialog">
        <div class="">
          <label for="fname">Import danh sách quà</label>
          <input name="fname" id="fname" type="file"
            accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
            @change="uploadFile" placeholder="File excel approve"><br>
          <a href="/template/template_import_gift.xlsx" download>Tải file mẫu!</a><br><br>
          <el-button type="success" icon="el-icon-circle-plus" @click="submitFile">
            Upload!
          </el-button>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button size="small" @click="dialogVisible = false">Đóng</el-button>
        </span>
      </el-dialog>
    </ValidationObserver>
  </div>
</template>

<script>
import { computed, ref, useContext } from "@nuxtjs/composition-api";
import { Message } from "element-ui";
import { integer } from "vee-validate/dist/rules";
import readXlsxFile from 'read-excel-file'
import { useGifts } from "~/composition";


export default {
  name: "ImportClaimApprove",
  props: {
    params: Object,
    approveTime: integer,
    title: String,
  },
  components: {},
  setup(props) {
    const { store } = useContext();
    const isLoading = ref(false);
    const dialogVisible = ref(false);
    const giftEntity = ref([])
    const { createGift, isSuccess } = useGifts();
    const uploadFile = async (event) => {
      const approveFile = event.target.files[0];
      readXlsxFile(approveFile).then((rows) => {
        rows.shift()
        if (rows.length > 0) {
          giftEntity.value = rows.filter(row => {
            let isValid = row.every(item => ((typeof item === 'number' && item > 0) || typeof item === 'string' || item === null))
            if (isValid) return row
          })
        }
      })
    };

    const handleBeforeCloseDialog = () => {

    }
    const submitFile = async (e) => {
      e.preventDefault()
      await store.dispatch('setLoading', true);

      if (giftEntity.value.length > 0) {
        await createGift(giftEntity.value).then(() => {
          if (!isSuccess.value) {
            Message({
              message: "Có lỗi khi import gift. Kiểm tra lại các bản ghi",
              type: 'error',
              duration: 5000,
            });
          } else {
            window.location.reload()
          }
        })
      } else {
        Message({
          message: "File không được để trống",
          type: 'error',
          duration: 5000,
        });
      }
      await store.dispatch('setLoading', false);
    };
    return {
      isLoading,
      uploadFile,
      submitFile,
      dialogVisible,
      handleBeforeCloseDialog
    }
  }

}
</script>

<style scoped></style>
