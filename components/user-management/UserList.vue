<template>
  <div class="card-body p-0">
    <table class="table table-hover align-items-center">
      <thead class="thead-light">
        <tr>
          <th>ID</th>
          <th>Email</th>
          <th>Role</th>
          <th><PERSON><PERSON><PERSON> động</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="user in users" :key="user.id">
          <td :class="{'text-danger': user.status <=1}">{{ user.id }}</td>
          <td :class="{'text-danger': user.status <=1}">{{ user.email }}</td>
          <td :class="{'text-danger': user.status <=1}">{{ user.role?.displayName || '-' }}</td>
          <td>
            <BaseButton type="primary" size="sm" class="me-1" @click="$emit('select', user)">
              Xem
            </BaseButton>
            <BaseButton 
              v-if="$permissions.canUpdateUser()"
              type="warning" 
              size="sm" 
              class="me-1"
              @click="editUser(user.id)"
            >
              Sửa
            </BaseButton>
    
          </td>
        </tr>
      </tbody>
    </table>
    <div v-if="!users.length" class="text-center py-4">Không có user nào</div>
    <div class="d-flex justify-content-center mt-3" v-if="total > limit">
      <BasePagination :value="page" :perPage="limit" :total="total" @input="onPageChange" />
    </div>
  </div>
</template>

<script setup>
import { useRouter } from '@nuxtjs/composition-api'
import BaseButton from '@/components/argon-core/BaseButton.vue'
import BasePagination from '@/components/argon-core/BasePagination.vue'

const router = useRouter()

const props = defineProps({
  users: {
    type: Array,
    default: () => []
  },
  page: {
    type: Number,
    default: 1
  },
  limit: {
    type: Number,
    default: 20
  },
  total: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['select', 'page-change'])

function onPageChange(newPage) {
  emit('page-change', newPage)
}

function editUser(userId) {
  console.log('Navigating to edit user page for ID:', userId)
  router.push(`/user-management/edit/${userId}`)
}
</script> 