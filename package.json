{"name": "urbox-evoucher-portal", "version": "1.0.0", "description": "Nuxt version of the Vue Argon Dashboard Pro", "author": "Creative Tim", "private": true, "scripts": {"dev": "nuxt", "build": "nuxt build", "start": "nuxt start", "generate": "nuxt generate"}, "dependencies": {"@nuxtjs/auth": "^4.9.1", "@nuxtjs/axios": "^5.12.1", "@nuxtjs/composition-api": "^0.33.1", "@nuxtjs/pwa": "^3.0.0-beta.19", "@nuxtjs/toast": "^3.3.1", "@reststate/vuex": "^0.2.2", "@tinymce/tinymce-vue": "^3.2.8", "axios": "1.8.2", "babel-plugin-module-resolver": "^4.1.0", "bootstrap": "4.3.1", "chart.js": "^2.9.3", "d3": "^5.7.0", "datamaps": "^0.5.9", "date-fns": "^1.30.1", "dropzone": "5.5.1", "element-ui": "^2.13.0", "es6-promise": "^4.1.1", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.25.2", "flatpickr": "^4.5.7", "form-data": "^4.0.0", "fuse.js": "^3.2.0", "google-maps": "^3.2.1", "he": "^1.2.0", "http-status": "^1.5.3", "js-sha256": "^0.9.0", "jsona": "^1.8.0", "lodash": "^4.17.20", "moment": "^2.29.1", "nuxt": "2.17.3", "perfect-scrollbar": "^1.3.0", "qs": "^6.9.4", "quill": "^1.3.6", "read-excel-file": "^5.6.1", "sass": "^1.64.1", "sweetalert2": "^9.5.4", "tinymce": "^7.9.1", "vee-validate": "^3.4.14", "vue-clipboard2": "^0.3.0", "vue-color": "^2.7.1", "vue-excel-xlsx": "^1.2.2", "vue-flatpickr-component": "^8.1.2", "vue-password-strength-meter": "^1.7.2", "vue-select": "^3.12.2", "vue-toast-notification": "^0.5.0", "vue2-editor": "^2.10.3", "vue2-transitions": "^0.2.3", "zxcvbn": "^4.4.2"}, "devDependencies": {"@babel/core": "^7.4.5", "babel-plugin-component": "^1.1.0", "cors": "^2.8.5", "cross-env": "^5.2.0", "nodemon": "^1.18.9", "request-ip": "^2.1.3", "sass-loader": "^10.1.1"}}