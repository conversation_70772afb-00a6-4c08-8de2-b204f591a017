<template>
  <client-only>
    <div class="wrapper">
      <LoadingContent v-if="!isUserLoaded && isLoading" />
      <notifications></notifications>
      <!-- Hiển thị layout ngay khi user đã đăng nhập -->
      <template v-if="$auth.loggedIn">
        <side-bar>
          <template slot="links">
            <sidebar-item v-if="isUserLoaded && $permissions.hasPermission('claims.read')" :link="{
              name: 'Đơn hàng',
              icon: 'ni ni-shop text-default',
              path: '/claims',
            }"></sidebar-item>
            <sidebar-item v-if="isUserLoaded && $permissions.canReadClaimsTemp()" :link="{
              name: 'Đ<PERSON>n hàng tạm',
              icon: 'ni ni-shop text-default',
              path: '/claims-temp',
            }"></sidebar-item>
            <sidebar-item v-if="isUserLoaded && $permissions.hasPermission('whitelist.read')" :link="{
              name: 'White list',
              icon: 'fa fa-list text-default',
              path: '/whitelist',
            }"></sidebar-item>
            <sidebar-item v-if="isUserLoaded && canCreateClaim" :link="{
              name: 'Tạo đơn hàng',
              icon: 'ni ni-align-left-2 text-default',
              path: '/claims/create',
            }"></sidebar-item>
            <sidebar-item v-if="isUserLoaded && $permissions.hasPermission('gifts.read')" :link="{
              name: 'Danh sách quà',
              icon: 'ni ni-align-left-2 text-default',
              path: '/gifts',
            }"></sidebar-item>
            <sidebar-item v-if="isUserLoaded && $permissions.hasPermission('campaigns.read')" :link="{
              name: 'Danh sách campaign',
              icon: 'ni ni-align-left-2 text-default',
              path: '/campaign',
            }"></sidebar-item>
            <sidebar-item v-if="isUserLoaded && $permissions.hasPermission('images.read')" :link="{
              name: 'Upload Image',
              icon: 'ni ni-align-left-2 text-default',
              path: '/images',
            }"></sidebar-item>
            <sidebar-item v-if="isUserLoaded && $permissions.hasPermission('users.read')" :link="{
              name: 'Quản lý user',
              icon: 'ni ni-single-02 text-default',
              path: '/user-management',
            }"></sidebar-item>
            <!--          <sidebar-item-->
            <!--              v-if="$store.getters.getCanCreateClaim"-->
            <!--              :link="{-->
            <!--                name: 'Upload ảnh',-->
            <!--                icon: 'ni ni-align-left-2 text-default',-->
            <!--                path: '/upload-images',-->
            <!--              }"-->
            <!--            ></sidebar-item>-->
          </template>
        </side-bar>
        <div class="main-content">
          <ClientOnly>
            <DashboardNavbar :type="$route.name === 'alternative' ? 'light' : 'default'" />
          </ClientOnly>
          <div @click="$sidebar.displaySidebar(false)">
            <nuxt></nuxt>
          </div>
          <content-footer v-if="!$route.meta.hideFooter"></content-footer>
        </div>
      </template>
      <!-- Nếu chưa đăng nhập, hiển thị thông báo -->
      <div v-else class="d-flex justify-content-center align-items-center" style="min-height: 100vh;">
        <div class="text-center">
          <p>Đang kiểm tra đăng nhập...</p>
        </div>
      </div>
    </div>
  </client-only>
</template>
<script>
/* eslint-disable no-new */
import PerfectScrollbar from 'perfect-scrollbar';
import 'perfect-scrollbar/css/perfect-scrollbar.css';
import { defineComponent, onMounted, useContext, useAsync, ref, getCurrentInstance } from '@nuxtjs/composition-api';

function hasElement(className) {
  return document.getElementsByClassName(className).length > 0;
}

function initScrollbar(className) {
  if (hasElement(className)) {
    new PerfectScrollbar(`.${className}`);
  } else {
    // try to init it later in case this component is loaded async
    setTimeout(() => {
      initScrollbar(className);
    }, 100);
  }
}

import DashboardNavbar from '~/components/layouts/DashboardNavbar.vue';
import ContentFooter from '~/components/layouts/ContentFooter.vue';
import LoadingContent from '@/components/LoadingContent.vue';
import { useApproval, useCampaign } from '~/composition';

export default defineComponent({
  components: {
    LoadingContent,
    DashboardNavbar,
    ContentFooter,
  },

  computed: {
    user() {
      return this.$auth.user;
    },
    // User loading state from store
    isUserLoaded() {
      return this.$store.getters.isUserLoaded;
    },
    isLoading() {
      return this.$store.getters.getIsLoading;
    },
    // All permission checks via $permissions plugin
    canCreateClaim() {
      if (this.isUserLoaded) {
        return this.$permissions.canCreateClaim();
      }
      return false;
    },
    canManageGifts() {
      if (this.isUserLoaded) {
        return this.$permissions.canManageGifts();
      }
      return false;
    },
    canManageCampaigns() {
      if (this.isUserLoaded) {
        return this.$permissions.canManageCampaigns();
      }
      return false;
    }
  },

  setup() {
    const { $axios, $auth, store, redirect } = useContext();

    onMounted(() => {
      // Chỉ khởi tạo scrollbar
      let isWindows = navigator.platform.startsWith('Win');
      if (isWindows) {
        initScrollbar('scrollbar-inner');
      }
    });
  },

  async created() {
    // Load user data ngay khi layout được tạo (nếu chưa có)
    if (this.$auth.loggedIn && !this.$store.getters.isUserLoaded && !this.$store.getters.isUserLoading) {
      try {
        console.log('🔧 Loading user data in layout created hook...');
        await this.$store.dispatch('loadUserData', {
          $axios: this.$axios,
          $auth: this.$auth
        });
        console.log('✅ User data loaded in layout');
      } catch (error) {
        console.error('❌ Failed to load user data in layout:', error);

        // Nếu lỗi 401, logout và redirect về login
        if (error?.response?.status === 401) {
          await this.$auth.logout();
          this.$router.push('/login');
        }
      }
    } else if (this.$store.getters.isUserLoaded) {
      console.log('⏭️ User data already loaded, skipping layout load');
    } else if (this.$store.getters.isUserLoading) {
      console.log('⏭️ User data currently loading, skipping layout load');
    }
  },

  watch: {
    // Theo dõi khi user data được load để kiểm tra permission
    isUserLoaded(newVal) {
      if (newVal) {
        console.log('🔧 User data loaded, checking route permission...');
        this.checkRoutePermission();
      }
    },
    // Theo dõi route changes để kiểm tra permission
    '$route'() {
      if (this.isUserLoaded) {
        this.checkRoutePermission();
      }
    },
    // Theo dõi auth state changes
    '$auth.loggedIn'(newVal) {
      console.log('🔧 Auth state changed:', newVal);
      if (newVal && !this.$store.getters.isUserLoaded && !this.$store.getters.isUserLoading) {
        console.log('🔧 User logged in but no user data, loading...');
        this.loadUserDataIfNeeded();
      }
    }
  },

  methods: {
    getPermissionForDynamicRoute(path) {
      // Kiểm tra claims detail route
      if (path.match(/^\/claims\/\d+$/)) {
        return 'claims.read';
      }

      // Kiểm tra claims-temp detail route  
      if (path.match(/^\/claims-temp\/\d+$/)) {
        return 'claims_temp.read';
      }

      // Kiểm tra whitelist detail route
      if (path.match(/^\/whitelist\/\d+$/)) {
        return 'whitelist.read';
      }

      return null;
    },
    checkRoutePermission() {
      const routePermissionMap = {
        '/claims': 'claims.read',
        '/claims/create': 'claims.create',
        '/claims-temp': 'claims_temp.read',
        '/gifts': 'gifts.read',
        '/campaign': 'campaigns.read',
        '/whitelist': 'whitelist.read',
        '/images': 'images.read',
        '/user-management': 'users.read',
      };

      const currentPath = this.$route.path;
      let requiredPermission = routePermissionMap[currentPath];

      // Nếu không tìm thấy trong static routes, kiểm tra dynamic routes
      if (!requiredPermission) {
        requiredPermission = this.getPermissionForDynamicRoute(currentPath);
      }

      if (requiredPermission && !this.$permissions.hasPermission(requiredPermission)) {
        console.warn('User does not have permission for route:', currentPath, 'Required:', requiredPermission);
        this.$router.push('/');
      }
    },
    async loadUserDataIfNeeded() {
      if (this.$auth.loggedIn && !this.$store.getters.isUserLoaded && !this.$store.getters.isUserLoading) {
        try {
          console.log('🔧 Loading user data when needed...');
          await this.$store.dispatch('loadUserData', {
            $axios: this.$axios,
            $auth: this.$auth
          });
          console.log('✅ User data loaded when needed');
        } catch (error) {
          console.error('❌ Failed to load user data when needed:', error);

          // Nếu lỗi 401, logout và redirect về login
          if (error?.response?.status === 401) {
            await this.$auth.logout();
            this.$router.push('/login');
          }
        }
      }
    }
  },
});
</script>
<style lang="scss"></style>
